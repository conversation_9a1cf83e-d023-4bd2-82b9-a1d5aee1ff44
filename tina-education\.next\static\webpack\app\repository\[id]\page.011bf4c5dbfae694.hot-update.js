"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/repository/[id]/page",{

/***/ "(app-pages-browser)/./app/repository/[id]/page.tsx":
/*!**************************************!*\
  !*** ./app/repository/[id]/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PublicationDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_HiOutlineHeart_react_icons_hi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=HiOutlineHeart!=!react-icons/hi */ \"(app-pages-browser)/./node_modules/react-icons/hi/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n// Sample related books data\nconst relatedBooks = [\n    {\n        id: \"1\",\n        title: \"The Silent Patient\",\n        author: \"Alex Michaelides\",\n        price: \"3000frs\",\n        type: \"Hardback\",\n        cover: \"/images/book-placeholder.jpg\"\n    },\n    {\n        id: \"2\",\n        title: \"Gone Girl\",\n        author: \"Gillian Flynn\",\n        price: \"2500frs\",\n        type: \"Paperback\",\n        cover: \"/images/book-placeholder.jpg\"\n    },\n    {\n        id: \"3\",\n        title: \"The Girl with the Dragon Tattoo\",\n        author: \"Stieg Larsson\",\n        price: \"3500frs\",\n        type: \"eBook\",\n        cover: \"/images/book-placeholder.jpg\"\n    },\n    {\n        id: \"4\",\n        title: \"Big Little Lies\",\n        author: \"Liane Moriarty\",\n        price: \"2800frs\",\n        type: \"Audiobook\",\n        cover: \"/images/book-placeholder.jpg\"\n    },\n    {\n        id: \"5\",\n        title: \"The Woman in the Window\",\n        author: \"A.J. Finn\",\n        price: \"3200frs\",\n        type: \"Hardback\",\n        cover: \"/images/book-placeholder.jpg\"\n    },\n    {\n        id: \"6\",\n        title: \"Sharp Objects\",\n        author: \"Gillian Flynn\",\n        price: \"2700frs\",\n        type: \"Paperback\",\n        cover: \"/images/book-placeholder.jpg\"\n    }\n];\n// Quantity Counter Component\nfunction QuantityCounter() {\n    _s();\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(1);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center bg-gray-300 border border-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setQuantity(Math.max(1, quantity - 1)),\n                className: \"w-8 h-6 flex items-center justify-center border-r border-black text-gray-800 hover:bg-gray-400\",\n                children: \"-\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-10 h-6 flex items-center justify-center border-r border-black text-gray-800\",\n                children: quantity\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setQuantity(quantity + 1),\n                className: \"w-8 h-6 flex items-center justify-center text-gray-800 hover:bg-gray-400\",\n                children: \"+\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n_s(QuantityCounter, \"Qe09P1MkhcuxrUT+4C5s8y/Npkg=\");\n_c = QuantityCounter;\nfunction PublicationDetailPage(param) {\n    let { params } = param;\n    var _publication_genre, _publication_user, _publication_genre1, _publication_user1;\n    _s1();\n    const [publication, setPublication] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"PublicationDetailPage.useEffect\": ()=>{\n            async function fetchPublication() {\n                try {\n                    const response = await fetch(\"/api/repository/publication/\".concat(params.id));\n                    if (response.ok) {\n                        const data = await response.json();\n                        setPublication(data);\n                    }\n                } catch (error) {\n                    console.error(\"Failed to fetch publication:\", error);\n                } finally{\n                    setLoading(false);\n                }\n            }\n            fetchPublication();\n        }\n    }[\"PublicationDetailPage.useEffect\"], [\n        params.id\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-white flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xl\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, this);\n    }\n    if (!publication) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-white flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xl\",\n                children: \"Publication not found\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white mt-18\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-18 flex items-center justify-center\",\n                style: {\n                    backgroundColor: \"#f0eded\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex space-x-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/books\",\n                            className: \"text-black text-xl hover:underline\",\n                            children: \"Books\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/authors\",\n                            className: \"text-black text-xl hover:underline\",\n                            children: \"Authors\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/journals\",\n                            className: \"text-black text-xl hover:underline\",\n                            children: \"Journals\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-10 py-4 border-b border-blue-500\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"text-s text-black\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"hover:underline\",\n                            children: \"Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this),\n                        ((_publication_genre = publication.genre) === null || _publication_genre === void 0 ? void 0 : _publication_genre.parent) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mx-2\",\n                                    children: \"/\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/books?genre=\".concat(publication.genre.parent.slug),\n                                    className: \"hover:underline\",\n                                    children: publication.genre.parent.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        publication.genre && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mx-2\",\n                                    children: \"/\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/books?genre=\".concat(publication.genre.slug),\n                                    className: \"hover:underline\",\n                                    children: publication.genre.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"mx-2\",\n                            children: \"/\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: publication.title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-10 py-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2/5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full max-w-sm\",\n                                children: publication.cover ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: publication.cover,\n                                    alt: publication.title,\n                                    width: 270,\n                                    height: 380,\n                                    className: \"w-full h-auto border border-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full aspect-[270/380] bg-gray-200 border border-gray-300 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-500 text-lg\",\n                                        children: \"No Cover Available\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-3/5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-black mb-4\",\n                                    children: publication.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-black mb-6\",\n                                    children: [\n                                        \"By \",\n                                        ((_publication_user = publication.user) === null || _publication_user === void 0 ? void 0 : _publication_user.name) || \"Unknown Author\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                    className: \"border-blue-500 mb-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex mt-20  gap-2.5 mb-35\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-30 h-16 border border-black bg-gray-300 flex flex-col items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-black\",\n                                                    children: \"Printed Copy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-black font-bold\",\n                                                    children: \"3000frs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-30 h-16 border border-black bg-gray-300 flex flex-col items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-black\",\n                                                    children: \"eBook\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-black font-bold\",\n                                                    children: \"2500frs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-30 h-16 border border-black bg-gray-300 flex flex-col items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-black\",\n                                                    children: \"Audio book\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-black font-bold\",\n                                                    children: \"3500frs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 mt-20 mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl text-black\",\n                                            children: \"Quantity:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuantityCounter, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-6 h-6 rounded-full bg-gray-400 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-600 font-bold\",\n                                                children: \"✓\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl text-black\",\n                                            children: \"In stock\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-8 py-4 w-60 h-11 text-white justify-center flex items-center text-xl\",\n                                            style: {\n                                                backgroundColor: \"#0c0a46\"\n                                            },\n                                            children: \"Add to Cart\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-3 py-2 w-60 h-11 bg-gray-300 border border-black flex items-center justify-center gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-700 text-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineHeart_react_icons_hi__WEBPACK_IMPORTED_MODULE_4__.HiOutlineHeart, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl text-black\",\n                                                    children: \"Add to wish list\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-10 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pe-20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl text-black font-bold mb-4\",\n                                        children: \"Description\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-black leading-relaxed\",\n                                        children: publication.abstract || \"A captivating mystery novel that will keep you on the edge of your seat. Follow the intricate plot as secrets unfold and characters navigate through unexpected twists and turns.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl text-black font-bold mb-4\",\n                                        children: \"Book Outline\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-black leading-relaxed\",\n                                        children: publication.content ? publication.content.substring(0, 200) + \"...\" : \"Chapter 1: The Beginning\\nChapter 2: The Mystery Unfolds\\nChapter 3: Clues and Revelations\\nChapter 4: The Truth Emerges\\nChapter 5: Resolution\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ps-30\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl text-black font-bold mb-4\",\n                                        children: \"Product Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-black space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Published:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"01 Feb 2024\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Genre:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: ((_publication_genre1 = publication.genre) === null || _publication_genre1 === void 0 ? void 0 : _publication_genre1.name) || \"Mystery\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Format:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: publication.type\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Edition:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"1st\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Extent:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"320\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"ISBN:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"xxxxxxxxxxxxx\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Imprint:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Tina Publishing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Dimension:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"320x153\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Publisher:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Tina Publishing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                        className: \"border-black mt-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-10 py-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl text-black font-bold mb-6\",\n                        children: \"About the Contributor\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-80 h-96 bg-gray-200 border border-gray-300 mb-4 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Contributor Image\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl text-black font-semibold mb-4\",\n                                children: ((_publication_user1 = publication.user) === null || _publication_user1 === void 0 ? void 0 : _publication_user1.name) || \"Unknown Author\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-md text-black max-w-2xl leading-relaxed\",\n                                children: \"An accomplished author with years of experience in writing compelling narratives. Known for creating intricate plots and memorable characters that resonate with readers worldwide.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-10 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl text-black font-bold mb-6 text-left\",\n                        children: \"Related Titles\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-6 gap-4\",\n                        children: relatedBooks.map((book)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full aspect-[159/256] bg-gray-200 border border-gray-300 mb-2 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: \"Book Cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-md text-black font-bold mb-1 line-clamp-2\",\n                                        children: book.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-black mb-1\",\n                                        children: book.author\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-black font-bold mb-1\",\n                                        children: book.price\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-black\",\n                                        children: book.type\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, book.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n                lineNumber: 358,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\repository\\\\[id]\\\\page.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n_s1(PublicationDetailPage, \"w0Cydr7maGHdbSof1bBfeo0XQRM=\");\n_c1 = PublicationDetailPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"QuantityCounter\");\n$RefreshReg$(_c1, \"PublicationDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/repository/[id]/page.tsx\n"));

/***/ })

});